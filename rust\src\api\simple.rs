use std::{
    io,
    sync::{<PERSON>, Mutex},
    time::Duration,
};

use flutter_rust_bridge::frb;
use serialport::SerialPort;

#[flutter_rust_bridge::frb(sync)] // Synchronous mode for simplicity of the demo
pub fn greet(name: String) -> String {
    format!("Hello, {name}!")
}

#[flutter_rust_bridge::frb(init)]
pub fn init_app() {
    // Default utilities - feel free to customize
    flutter_rust_bridge::setup_default_user_utils();
}

#[frb(unignore)]
pub struct MyStruct {
    pub name: String,
    pub age: u32,
}

impl MyStruct {
    #[frb(sync)]
    pub fn new(name: String, age: u32) -> Self {
        Self { name, age }
    }

    #[frb] // This allows the method to be called synchronously from Dart
    pub fn greet(&self) -> String {
        format!(
            "Hello, my name is {} and I am {} years old.",
            self.name, self.age
        )
    }
}
#[frb(opaque)]
pub struct FlutterRs {
    pub port_name: String,
    pub baud_rate: u32,
    // pub data_bits: u8,
    // pub stop_bits: u8,
    // pub parity: u8,

    // private fields
    _port: Option<Arc<SafeSerialPort>>,
}

impl FlutterRs {
    #[frb(sync)]
    pub fn new(
        port_name: String,
        baud_rate: u32,
        // data_bits: u8,
        // stop_bits: u8,
        // parity: u8,
    ) -> Self {
        Self {
            port_name,
            baud_rate,
            // data_bits,
            // stop_bits,
            // parity,
            _port: None,
        }
    }

    #[frb]
    pub fn open(&mut self) -> io::Result<()> {
        if self._port.is_some() {
            return Err(io::Error::new(
                io::ErrorKind::AlreadyExists,
                "Port is already open",
            ));
        }

        self._port = Some(Arc::new(SafeSerialPort::new(
            &self.port_name,
            self.baud_rate,
        )));
        Ok(())
    }

    #[frb]
    pub fn read(&self) -> io::Result<Vec<u8>> {
        //TODO: handle timeout
        if self._port.is_none() {
            return Err(io::Error::new(io::ErrorKind::NotFound, "Port is not open"));
        }

        let port = self._port.clone();

        let mut buff = vec![0; 1024];

        match port.unwrap().read(&mut buff) {
            Ok(size) => Ok(buff[..size].to_vec()),
            Err(ref e) if e.kind() == io::ErrorKind::TimedOut => Ok(vec![]),
            Err(e) => Err(e),
        }
    }

    #[frb]
    pub fn write(&self, output: Vec<u8>) -> io::Result<usize> {
        if self._port.is_none() {
            return Err(io::Error::new(io::ErrorKind::NotFound, "Port is not open"));
        }

        let port = self._port.clone();

        port.unwrap().write(&output)
    }
}

#[cfg(test)]
mod tests {
    use std::io::Write;

    use super::*;

    #[test]
    fn test_greet() {
        assert_eq!(greet("Tom".to_string()), "Hello, Tom!");
    }

    #[test]
    fn test_safe_rs() {
        let mut rs = FlutterRs::new("COM15".to_string(), 115200);
        rs.open().unwrap();

        for _ in 0..10 {
            let buf = rs.read().unwrap();
            println!("{:?}", buf);
        }
    }

    #[test]
    fn test_raw_rs_lib() {
        let ports = serialport::available_ports().expect("No ports found!");
        for p in ports {
            println!("{}", p.port_name);
        }

        let mut port = serialport::new("COM15", 115_200)
            // .timeout(Duration::from_millis(1000))
            .open()
            .unwrap();

        let mut buff = vec![0; 256];
        let size = port.read(&mut buff).unwrap();
        buff.truncate(size);
        println!("{:?}", buff);
    }

    #[test]
    fn test_raw2() {
        let port_name = "COM15";
        let baud_rate = 115_200;

        let port = serialport::new(port_name, baud_rate)
            .timeout(Duration::from_millis(1000))
            .open();

        match port {
            Ok(mut port) => {
                let mut serial_buf: Vec<u8> = vec![0; 1024];
                println!("Receiving data on {} at {} baud:", &port_name, &baud_rate);
                loop {
                    match port.read(serial_buf.as_mut_slice()) {
                        Ok(t) => {
                            io::stdout().write_all(&serial_buf[..t]).unwrap();
                            io::stdout().flush().unwrap();
                        }
                        Err(ref e) if e.kind() == io::ErrorKind::TimedOut => (),
                        Err(e) => eprintln!("{:?}", e),
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to open \"{}\". Error: {}", port_name, e);
                ::std::process::exit(1);
            }
        }
    }
}

struct SafeSerialPort {
    inner: Arc<Mutex<Box<dyn SerialPort>>>,
    read_lock: Arc<Mutex<()>>,
    write_lock: Arc<Mutex<()>>,
}

impl SafeSerialPort {
    fn new(name: &str, baud: u32) -> Self {
        let port = serialport::new(name, baud)
            .timeout(Duration::from_millis(1000))
            .open()
            .unwrap();

        Self {
            inner: Arc::new(Mutex::new(port)),
            read_lock: Arc::new(Mutex::new(())),
            write_lock: Arc::new(Mutex::new(())),
        }
    }

    fn read(&self, buf: &mut [u8]) -> io::Result<usize> {
        let _lock = self.read_lock.lock().unwrap();
        self.inner.lock().unwrap().read(buf)
    }

    fn write(&self, buf: &[u8]) -> io::Result<usize> {
        let _lock = self.write_lock.lock().unwrap();
        self.inner.lock().unwrap().write(buf)
    }
}
